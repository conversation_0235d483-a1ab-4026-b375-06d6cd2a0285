using System.Text;
using Application.Apis.Clients;
using Dxf.Core.Extensions;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using IxMilia.Dxf;
using Polly;
using Microsoft.Extensions.Logging;
using Slide.Core;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;
using TriangleNet.Meshing;
using TriangleNet.Meshing.Algorithm;
using Workflow.Extensions;
using static Workflow.Constants.SectionWorkFlow;
using Activity = Elsa.Services.Activity;
using File = Application.Apis._Shared.File;

namespace Workflow.Section.Activities
{
    public class CreateInitialSliFileActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<CreateInitialSliFileActivity> _logger;
        private static readonly ConstraintOptions ConstraintOptions = new ()
        {
            ConformingDelaunay = true,
            Convex = false,
            SegmentSplitting = 2
        };
        private static readonly ConstraintOptions FallbackConstraintOptions = new ()
        {
            ConformingDelaunay = true,
            Convex = false,
            SegmentSplitting = 0
        };

        public CreateInitialSliFileActivity(
            IClientsApiService clientsApiService,
            ILogger<CreateInitialSliFileActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sectionId = (context.GetTransientVariable<Domain.Messages.Commands.Section.CreateSliFile>(Variables.Command)).SectionId;

                var section = await _clientsApiService
                    .GetSectionById(sectionId);

                if (section == null)
                {
                    _logger.LogSectionNotFound(sectionId);
                    return Done();
                }

                foreach (var sectionReview in section.Reviews)
                {
                    if (sectionReview.ConstructionStages.Any())
                    {
                        foreach (var constructionStage in sectionReview.ConstructionStages)
                        {
                            var constructionStageSli = MountSliFile(constructionStage.Drawing);

                            if (constructionStageSli == null)
                            {
                                continue;
                            }

                            var result = await _clientsApiService.AddSliToSectionReview(new()
                            {
                                SectionId = sectionId,
                                ReviewId = sectionReview.Id,
                                ConstructionStageId = constructionStage.Id,
                                Sli = constructionStageSli
                            });

                            if (!result.IsSuccessStatusCode)
                            {
                                _logger.LogStageSliIntegrationError(sectionId);
                                return Fault("Error adding SLI to construction stage.");
                            }
                        }

                        continue;
                    }

                    var reviewSli = MountSliFile(sectionReview.Drawing);

                    if (reviewSli == null)
                    {
                        continue;
                    }

                    var resultSectionReview = await _clientsApiService.AddSliToSectionReview(new()
                    {
                        SectionId = sectionId,
                        ReviewId = sectionReview.Id,
                        Sli = reviewSli
                    });

                    if (!resultSectionReview.IsSuccessStatusCode)
                    {
                        _logger.LogAddSliToSectionReviewError();
                        return Fault("Error adding SLI to section review.");
                    }
                }

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogCreateInitialSliFileError(e);
                return Fault(e);
            }
        }

        public File MountSliFile(File dxfFile)
        {
            if (dxfFile == null || string.IsNullOrEmpty(dxfFile.Base64))
            {
                return null;
            }

            var dxf = new DxfFile().LoadWithBase64(dxfFile.Base64);

            var externalVertices = dxf.GetExternalVertices();

            if (externalVertices.Count == 0)
            {
                _logger.LogNoExternalVerticesFound();
                return null;
            }

            // Send the first vertex to the end of the list
            externalVertices = externalVertices.Skip(1).Concat(externalVertices.Take(1)).ToList();

            var materialVertices = dxf.GetMaterialVertices();

            if (materialVertices.Count == 0)
            {
                _logger.LogNoMaterialsFound();
                return null;
            }

            var fallbackPolicy = Policy<IMesh>
                .Handle<Exception>()
                .Fallback(
                    fallbackAction: () => RunFallbackTriangulation(externalVertices, materialVertices));

            var mesh = fallbackPolicy.Execute(() => RunDefaultTriangulation(externalVertices, materialVertices));

            var sliFile = new SliFile()
                .FromDxf(externalVertices, materialVertices, mesh);

            var textBytes = Encoding.UTF8.GetBytes(sliFile.Save());

            return new()
            {
                Base64 = Convert.ToBase64String(textBytes),
                Name = $"{DateTime.Now:ddMMyyyy_HHmmss}.sli"
            };
        }

        private static IMesh RunDefaultTriangulation(
            List<Vertice> externalVertices,
            List<(Vertice, int)> materialVertices)
        {
            var polygon = new Polygon()
                .AddExternal(externalVertices)
                .AddMaterials(materialVertices);

            return polygon.Triangulate(ConstraintOptions);
        }

        private static IMesh RunFallbackTriangulation(
            List<Vertice> externalVertices,
            List<(Vertice, int)> materialVertices)
        {
            var polygon = new Polygon()
                .AddExternal(externalVertices)
                .AddMaterialsWithDeduplication(materialVertices);

            var isConsistent = TriangleNet.Tools.PolygonValidator.IsConsistent(polygon);
            var hasDuplicateVertices = TriangleNet.Tools.PolygonValidator.HasDuplicateVertices(polygon);
            var segmentRatio = TriangleNet.Tools.PolygonValidator.GetSegmentRatio(polygon, 0.001);
            var hasBadAngles = TriangleNet.Tools.PolygonValidator.HasBadAngles(polygon);

            return polygon.Triangulate(FallbackConstraintOptions, new QualityOptions(), new Incremental());
        }
    }
}
