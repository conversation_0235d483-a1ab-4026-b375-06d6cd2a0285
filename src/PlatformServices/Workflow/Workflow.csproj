<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Structure\**" />
    <EmbeddedResource Remove="Structure\**" />
    <None Remove="Structure\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CronExpressionDescriptor" Version="2.33.0" />
    <PackageReference Include="Elsa" Version="2.14.1" />
    <PackageReference Include="Elsa.Activities.Console" Version="2.14.1" />
    <PackageReference Include="Elsa.Activities.Email" Version="2.14.1" />
    <PackageReference Include="Elsa.Activities.MassTransit" Version="2.14.1" />
    <PackageReference Include="Elsa.Activities.Temporal.Quartz" Version="2.14.1" />
    <PackageReference Include="Elsa.Core" Version="2.14.1" />
    <PackageReference Include="Elsa.Designer.Components.Web" Version="2.14.1" />
    <PackageReference Include="Elsa.DistributedLocking.SqlServer" Version="2.14.1" />
    <PackageReference Include="Elsa.Persistence.EntityFramework.SqlServer" Version="2.14.1" />
    <PackageReference Include="Elsa.Retention" Version="2.14.1" />
    <PackageReference Include="Elsa.Server.Api" Version="2.14.1" />
    <PackageReference Include="FuzzySharp" Version="2.0.2" />
    <PackageReference Include="IxMilia.Dxf" Version="0.8.3" />
    <PackageReference Include="NetTopologySuite" Version="2.6.0" />
    <PackageReference Include="NPOI" Version="2.7.1" />
    <PackageReference Include="PDFsharp-MigraDoc" Version="6.1.1" />
    <PackageReference Include="SkiaSharp" Version="2.88.8" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.88.8" /> 
    <PackageReference Include="Speckle.Triangle" Version="1.0.0" />
    <PackageReference Include="Svg.Skia" Version="2.0.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Application\Application.csproj" />
    <ProjectReference Include="..\Database\Database.csproj" />
    <ProjectReference Include="..\Domain\Domain.csproj" />
    <ProjectReference Include="..\Dxf.Core\Dxf.Core.csproj" />
    <ProjectReference Include="..\Geometry.Core\Geometry.Core.csproj" />
    <ProjectReference Include="..\Slide.Core\Slide.Core.csproj" />
  </ItemGroup>

</Project>
