using Geometry.Core;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;

namespace Workflow.Extensions;

public static class PolygonExtensions
{
    public static Polygon AddExternal(
        this Polygon polygon,
        List<Vertice> externalVertices)
    {
        if (externalVertices is null || !externalVertices.Any())
        {
            return polygon;
        }
        
        externalVertices = externalVertices.RemoveCloseVertices();
        
        var contour = new Contour(
            externalVertices.Select(x => new Vertex(x.X, x.Y, 1)),
            1);

        polygon.Add(contour);

        return polygon;
    }

    public static Polygon AddMaterials(
        this Polygon polygon,
        List<(Vertice Vertices, int MaterialId)> materials)
    {
        if (materials is null || !materials.Any())
        {
            return polygon;
        }

        materials
            .GroupBy(x => x.Item2)
            .SelectMany(group => group.Take(group.Count() - 1)
                .Zip(
                    group.Skip(1),
                    (element1, element2) => new
                    {
                        Element1 = element1.Item1, Element2 = element2.Item1
                    })
                .Where(data => data.Element1 != null || data.Element2 != null)
                .Select(data => new
                {
                    Vertices = new[] { data.Element1, data.Element2 }
                        .Where(el => el != null)
                        .Select(el => new Vertex(el.X, el.Y))
                        .ToArray()
                }))
            .ToList()
            .ForEach(data =>
            {
                if (data.Vertices.Length > 1)
                {
                    polygon.Add(
                        new Segment(data.Vertices[0], data.Vertices[1], 1),
                        1);
                }

                foreach (Vertex vertex in data.Vertices)
                {
                    polygon.Points.Add(vertex);
                }
            });

        return polygon;
    }
    
    public static Polygon AddMaterialsWithDeduplication(
        this Polygon polygon,
        List<(Vertice Vertice, int MaterialId)> materials)
    {
        if (materials is null || !materials.Any())
        {
            return polygon;
        }
        
        var allValidMaterialVertices = materials
            .Select(x => x.Vertice)
            .ToList()
            .RemoveCloseVertices();
        
        var groupedMaterials = materials
            .Where(item => allValidMaterialVertices.Any(validVertex => (PointD)validVertex == (PointD)item.Vertice))
            .GroupBy(item => item.MaterialId);
        
        foreach (var group in groupedMaterials)
        {
            var vertices = group.Select(item => new Vertex(item.Vertice.X, item.Vertice.Y)).ToList();

            for (var i = 0; i < vertices.Count - 1; i++)
            {
                var existingVertex1 = polygon.Points.FirstOrDefault(vertex => vertex == vertices[i]);
                var existingVertex2 = polygon.Points.FirstOrDefault(vertex => vertex == vertices[i + 1]);
                var vertex1 = existingVertex1 ?? vertices[i];
                var vertex2 = existingVertex2 ?? vertices[i + 1];
                var segment = new Segment(vertex1, vertex2, 1);

                if (polygon.Segments.Any(s => s.GetVertex(0) == segment.GetVertex(0) && s.GetVertex(1) == segment.GetVertex(1)))
                {
                    continue;
                }
                
                Action action = (existingVertex1, existingVertex2) switch
                {
                    (null, null) => () => polygon.Add(segment, true),
                    (null, _) => () => polygon.Add(segment, 0),
                    (_, null) =>  () => polygon.Add(segment, 1),
                    (_, _) => () =>  polygon.Add(segment)
                };

                action();
            }
        }

        return polygon;
    }
}