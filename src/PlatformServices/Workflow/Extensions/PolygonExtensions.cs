using Geometry.Core;
using Slide.Core.Objects.Sli;
using TriangleNet.Geometry;
using NtsGeometry = NetTopologySuite.Geometries.Geometry;
using NtsPolygon = NetTopologySuite.Geometries.Polygon;
using NtsCoordinate = NetTopologySuite.Geometries.Coordinate;

namespace Workflow.Extensions;

public static class PolygonExtensions
{
    public static Polygon AddExternal(
        this Polygon polygon,
        List<Vertice> externalVertices)
    {
        if (externalVertices is null || !externalVertices.Any())
        {
            return polygon;
        }
        
        externalVertices = externalVertices.RemoveCloseVertices();
        
        var contour = new Contour(
            externalVertices.Select(x => new Vertex(x.X, x.Y, 1)),
            1);

        polygon.Add(contour);

        return polygon;
    }

    public static Polygon AddMaterials(
        this Polygon polygon,
        List<(Vertice Vertices, int MaterialId)> materials)
    {
        if (materials is null || !materials.Any())
        {
            return polygon;
        }

        materials
            .GroupBy(x => x.Item2)
            .SelectMany(group => group.Take(group.Count() - 1)
                .Zip(
                    group.Skip(1),
                    (element1, element2) => new
                    {
                        Element1 = element1.Item1, Element2 = element2.Item1
                    })
                .Where(data => data.Element1 != null || data.Element2 != null)
                .Select(data => new
                {
                    Vertices = new[] { data.Element1, data.Element2 }
                        .Where(el => el != null)
                        .Select(el => new Vertex(el.X, el.Y))
                        .ToArray()
                }))
            .ToList()
            .ForEach(data =>
            {
                if (data.Vertices.Length > 1)
                {
                    polygon.Add(
                        new Segment(data.Vertices[0], data.Vertices[1], 1),
                        1);
                }

                foreach (Vertex vertex in data.Vertices)
                {
                    polygon.Points.Add(vertex);
                }
            });

        return polygon;
    }
    
    public static Polygon AddMaterialsWithDeduplication(
        this Polygon polygon,
        List<(Vertice Vertice, int MaterialId)> materials)
    {
        if (materials is null || !materials.Any())
        {
            return polygon;
        }
        
        var allValidMaterialVertices = materials
            .Select(x => x.Vertice)
            .ToList()
            .RemoveCloseVertices();
        
        var groupedMaterials = materials
            .Where(item => allValidMaterialVertices.Any(validVertex => (PointD)validVertex == (PointD)item.Vertice))
            .GroupBy(item => item.MaterialId);
        
        foreach (var group in groupedMaterials)
        {
            var vertices = group.Select(item => new Vertex(item.Vertice.X, item.Vertice.Y)).ToList();

            for (var i = 0; i < vertices.Count - 1; i++)
            {
                var existingVertex1 = polygon.Points.FirstOrDefault(vertex => vertex == vertices[i]);
                var existingVertex2 = polygon.Points.FirstOrDefault(vertex => vertex == vertices[i + 1]);
                var vertex1 = existingVertex1 ?? vertices[i];
                var vertex2 = existingVertex2 ?? vertices[i + 1];
                var segment = new Segment(vertex1, vertex2, 1);

                if (polygon.Segments.Any(s => s.GetVertex(0) == segment.GetVertex(0) && s.GetVertex(1) == segment.GetVertex(1)))
                {
                    continue;
                }
                
                Action action = (existingVertex1, existingVertex2) switch
                {
                    (null, null) => () => polygon.Add(segment, true),
                    (null, _) => () => polygon.Add(segment, 0),
                    (_, null) =>  () => polygon.Add(segment, 1),
                    (_, _) => () =>  polygon.Add(segment)
                };

                action();
            }
        }

        return polygon;
    }

    #region NetTopologySuite Overloads

    /// <summary>
    /// Adds external vertices from a NetTopologySuite geometry to the polygon as an external contour.
    /// This method extracts coordinates from the geometry and converts them to vertices before
    /// delegating to the existing AddExternal method.
    /// </summary>
    /// <param name="polygon">The TriangleNet polygon to add the external vertices to.</param>
    /// <param name="externalGeometry">The NetTopologySuite geometry containing the external vertices.
    /// Can be a Polygon, LineString, Point, or any other geometry type.</param>
    /// <returns>The modified polygon with the external vertices added.</returns>
    /// <exception cref="ArgumentNullException">Thrown when externalGeometry is null.</exception>
    /// <exception cref="ArgumentException">Thrown when externalGeometry is empty or contains no valid coordinates.</exception>
    /// <remarks>
    /// This overload provides NetTopologySuite compatibility for the existing AddExternal functionality.
    /// The geometry coordinates are extracted and converted to Vertice objects, then processed using
    /// the same logic as the original method, including close vertex removal.
    /// </remarks>
    public static Polygon AddExternal(
        this Polygon polygon,
        NtsGeometry externalGeometry)
    {
        ValidateGeometry(externalGeometry, nameof(externalGeometry));

        var coordinates = ExtractCoordinatesFromGeometry(externalGeometry);
        var vertices = ConvertCoordinatesToVertices(coordinates);

        return polygon.AddExternal(vertices);
    }

    /// <summary>
    /// Adds material vertices from a NetTopologySuite geometry to the polygon with a specified material ID.
    /// This method extracts coordinates from the geometry, converts them to vertices, and associates
    /// them with the provided material ID before delegating to the existing AddMaterials method.
    /// </summary>
    /// <param name="polygon">The TriangleNet polygon to add the material vertices to.</param>
    /// <param name="materialGeometry">The NetTopologySuite geometry containing the material vertices.
    /// Can be a Polygon, LineString, Point, or any other geometry type.</param>
    /// <param name="materialId">The material ID to associate with all vertices from the geometry.</param>
    /// <returns>The modified polygon with the material vertices added.</returns>
    /// <exception cref="ArgumentNullException">Thrown when materialGeometry is null.</exception>
    /// <exception cref="ArgumentException">Thrown when materialGeometry is empty or contains no valid coordinates.</exception>
    /// <remarks>
    /// This overload provides NetTopologySuite compatibility for the existing AddMaterials functionality.
    /// All coordinates from the geometry are assigned the same material ID. The geometry coordinates
    /// are extracted and converted to Vertice objects, then processed using the same logic as the
    /// original method, including grouping and segment creation.
    /// </remarks>
    public static Polygon AddMaterials(
        this Polygon polygon,
        NtsGeometry materialGeometry,
        int materialId)
    {
        ValidateGeometry(materialGeometry, nameof(materialGeometry));

        var coordinates = ExtractCoordinatesFromGeometry(materialGeometry);
        var vertices = ConvertCoordinatesToVertices(coordinates);

        var materials = vertices.Select(vertex => (vertex, materialId)).ToList();

        return polygon.AddMaterials(materials);
    }

    /// <summary>
    /// Adds material vertices from a NetTopologySuite geometry to the polygon with deduplication
    /// and a specified material ID. This method extracts coordinates from the geometry, converts
    /// them to vertices, and associates them with the provided material ID before delegating to
    /// the existing AddMaterialsWithDeduplication method.
    /// </summary>
    /// <param name="polygon">The TriangleNet polygon to add the material vertices to.</param>
    /// <param name="materialGeometry">The NetTopologySuite geometry containing the material vertices.
    /// Can be a Polygon, LineString, Point, or any other geometry type.</param>
    /// <param name="materialId">The material ID to associate with all vertices from the geometry.</param>
    /// <returns>The modified polygon with the material vertices added using deduplication logic.</returns>
    /// <exception cref="ArgumentNullException">Thrown when materialGeometry is null.</exception>
    /// <exception cref="ArgumentException">Thrown when materialGeometry is empty or contains no valid coordinates.</exception>
    /// <remarks>
    /// This overload provides NetTopologySuite compatibility for the existing AddMaterialsWithDeduplication functionality.
    /// All coordinates from the geometry are assigned the same material ID. The geometry coordinates
    /// are extracted and converted to Vertice objects, then processed using the same deduplication
    /// logic as the original method, including close vertex removal and segment deduplication.
    /// </remarks>
    public static Polygon AddMaterialsWithDeduplication(
        this Polygon polygon,
        NtsGeometry materialGeometry,
        int materialId)
    {
        ValidateGeometry(materialGeometry, nameof(materialGeometry));

        var coordinates = ExtractCoordinatesFromGeometry(materialGeometry);
        var vertices = ConvertCoordinatesToVertices(coordinates);

        var materials = vertices.Select(vertex => (vertex, materialId)).ToList();

        return polygon.AddMaterialsWithDeduplication(materials);
    }

    /// <summary>
    /// Adds material vertices from multiple NetTopologySuite geometries to the polygon, where each
    /// geometry can have a different material ID. This method extracts coordinates from each geometry,
    /// converts them to vertices, and associates them with their respective material IDs.
    /// </summary>
    /// <param name="polygon">The TriangleNet polygon to add the material vertices to.</param>
    /// <param name="materialGeometries">A collection of tuples containing NetTopologySuite geometries
    /// and their associated material IDs.</param>
    /// <returns>The modified polygon with all material vertices added.</returns>
    /// <exception cref="ArgumentNullException">Thrown when materialGeometries is null.</exception>
    /// <exception cref="ArgumentException">Thrown when any geometry in the collection is null, empty, or contains no valid coordinates.</exception>
    /// <remarks>
    /// This overload provides NetTopologySuite compatibility for the existing AddMaterials functionality
    /// with support for multiple geometries with different material IDs. Each geometry's coordinates
    /// are extracted and converted to Vertice objects, then processed using the same logic as the
    /// original method, including grouping and segment creation.
    /// </remarks>
    public static Polygon AddMaterials(
        this Polygon polygon,
        IEnumerable<(NtsGeometry Geometry, int MaterialId)> materialGeometries)
    {
        if (materialGeometries == null)
            throw new ArgumentNullException(nameof(materialGeometries));

        var allMaterials = new List<(Vertice Vertices, int MaterialId)>();

        foreach (var (geometry, materialId) in materialGeometries)
        {
            ValidateGeometry(geometry, $"{nameof(materialGeometries)}.Geometry");

            var coordinates = ExtractCoordinatesFromGeometry(geometry);
            var vertices = ConvertCoordinatesToVertices(coordinates);

            allMaterials.AddRange(vertices.Select(vertex => (vertex, materialId)));
        }

        return polygon.AddMaterials(allMaterials);
    }

    /// <summary>
    /// Adds material vertices from multiple NetTopologySuite geometries to the polygon with deduplication,
    /// where each geometry can have a different material ID. This method extracts coordinates from each
    /// geometry, converts them to vertices, and associates them with their respective material IDs.
    /// </summary>
    /// <param name="polygon">The TriangleNet polygon to add the material vertices to.</param>
    /// <param name="materialGeometries">A collection of tuples containing NetTopologySuite geometries
    /// and their associated material IDs.</param>
    /// <returns>The modified polygon with all material vertices added using deduplication logic.</returns>
    /// <exception cref="ArgumentNullException">Thrown when materialGeometries is null.</exception>
    /// <exception cref="ArgumentException">Thrown when any geometry in the collection is null, empty, or contains no valid coordinates.</exception>
    /// <remarks>
    /// This overload provides NetTopologySuite compatibility for the existing AddMaterialsWithDeduplication
    /// functionality with support for multiple geometries with different material IDs. Each geometry's
    /// coordinates are extracted and converted to Vertice objects, then processed using the same
    /// deduplication logic as the original method, including close vertex removal and segment deduplication.
    /// </remarks>
    public static Polygon AddMaterialsWithDeduplication(
        this Polygon polygon,
        IEnumerable<(NtsGeometry Geometry, int MaterialId)> materialGeometries)
    {
        if (materialGeometries == null)
            throw new ArgumentNullException(nameof(materialGeometries));

        var allMaterials = new List<(Vertice Vertice, int MaterialId)>();

        foreach (var (geometry, materialId) in materialGeometries)
        {
            ValidateGeometry(geometry, $"{nameof(materialGeometries)}.Geometry");

            var coordinates = ExtractCoordinatesFromGeometry(geometry);
            var vertices = ConvertCoordinatesToVertices(coordinates);

            allMaterials.AddRange(vertices.Select(vertex => (vertex, materialId)));
        }

        return polygon.AddMaterialsWithDeduplication(allMaterials);
    }

    /// <summary>
    /// Converts NetTopologySuite coordinates to Vertice objects.
    /// </summary>
    /// <param name="coordinates">The coordinates to convert.</param>
    /// <returns>A list of Vertice objects.</returns>
    private static List<Vertice> ConvertCoordinatesToVertices(NtsCoordinate[] coordinates)
    {
        if (coordinates == null || coordinates.Length == 0)
            return new List<Vertice>();

        return coordinates
            .Where(coord => coord != null && !double.IsNaN(coord.X) && !double.IsNaN(coord.Y))
            .Select(coord => new Vertice(coord.X, coord.Y))
            .ToList();
    }

    /// <summary>
    /// Extracts coordinates from a NetTopologySuite geometry.
    /// </summary>
    /// <param name="geometry">The geometry to extract coordinates from.</param>
    /// <returns>An array of coordinates.</returns>
    private static NtsCoordinate[] ExtractCoordinatesFromGeometry(NtsGeometry geometry)
    {
        if (geometry == null)
            return Array.Empty<NtsCoordinate>();

        return geometry switch
        {
            NtsPolygon polygon => polygon.ExteriorRing.Coordinates,
            NetTopologySuite.Geometries.LineString lineString => lineString.Coordinates,
            NetTopologySuite.Geometries.Point point => new[] { point.Coordinate },
            NetTopologySuite.Geometries.MultiPolygon multiPolygon => multiPolygon.Geometries
                .OfType<NtsPolygon>()
                .SelectMany(p => p.ExteriorRing.Coordinates)
                .ToArray(),
            NetTopologySuite.Geometries.GeometryCollection collection => collection.Geometries
                .SelectMany(g => ExtractCoordinatesFromGeometry(g))
                .ToArray(),
            _ => geometry.Coordinates
        };
    }

    /// <summary>
    /// Validates that a NetTopologySuite geometry is not null and contains valid coordinates.
    /// </summary>
    /// <param name="geometry">The geometry to validate.</param>
    /// <param name="parameterName">The name of the parameter for exception messages.</param>
    /// <exception cref="ArgumentNullException">Thrown when geometry is null.</exception>
    /// <exception cref="ArgumentException">Thrown when geometry is empty or invalid.</exception>
    private static void ValidateGeometry(NtsGeometry geometry, string parameterName)
    {
        if (geometry == null)
            throw new ArgumentNullException(parameterName);

        if (geometry.IsEmpty)
            throw new ArgumentException("Geometry cannot be empty.", parameterName);

        var coordinates = ExtractCoordinatesFromGeometry(geometry);
        if (coordinates.Length == 0)
            throw new ArgumentException("Geometry must contain at least one valid coordinate.", parameterName);
    }

    #endregion
}