using Slide.Core.Objects.Sli;

namespace Workflow.Extensions;

public static class VerticeExtensions
{
    /// <summary>
    /// Removes duplicate vertices from the collection by keeping only the first occurrence of vertices 
    /// that are within tolerance distance of each other. Uses the IsCloseTo method which has a 
    /// tolerance of 0.001 units.
    /// </summary>
    /// <param name="vertices">The collection of vertices to process.</param>
    /// <returns>
    /// A new list containing only unique vertices, where vertices within tolerance distance 
    /// are considered duplicates. Returns an empty list if the input is null or empty.
    /// Null vertices within the collection are filtered out.
    /// </returns>
    /// <remarks>
    /// This method has O(n²) time complexity in the worst case. For large collections (>1000 vertices),
    /// consider using spatial indexing techniques for better performance.
    /// The first occurrence of close vertices is kept, subsequent close vertices are removed.
    /// </remarks>
    public static List<Vertice> RemoveCloseVertices(this List<Vertice> vertices)
    {
        if (vertices == null || vertices.Count == 0)
            return new List<Vertice>();

        var uniqueVertices = new List<Vertice>();

        foreach (var vertex in vertices)
        {
            if (vertex == null)
            {
                continue;
            }

            // Check if this vertex is close to any already added vertex
            var isCloseToExisting = uniqueVertices.Any(existing => existing.IsCloseTo(vertex));

            if (!isCloseToExisting)
            {
                uniqueVertices.Add(vertex);
            }
        }

        return uniqueVertices;
    }
}