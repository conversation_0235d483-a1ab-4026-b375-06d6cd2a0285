using Coordinate.Core.Enums;
using System.Text.Json.Serialization;

namespace Application.Apis.Clients.Response.Section
{
    public sealed record SectionCoordinates
    {
        [JsonPropertyName("datum")]
        public Datum Datum { get; set; }

        [JsonPropertyName("upstream_coordinate_setting")]
        public SectionCoordinateSetting UpstreamCoordinateSetting { get; set; }

        [JsonPropertyName("downstream_coordinate_setting")]
        public SectionCoordinateSetting DownstreamCoordinateSetting { get; set; }

        [JsonPropertyName("midpoint_coordinate_setting")]
        public SectionCoordinateSetting MidpointCoordinateSetting { get; set; }
    }
}
