using Application.Email;
using Elsa.Activities.Email.Options;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using System.Net;

namespace Application
{
    public static class EmailCfg
    {
        public static IServiceCollection AddEmailService(this IServiceCollection services, SmtpOptions smtpOptions)
        {
            services.AddTransient<IEmailService, EmailService>(x => new EmailService(new(smtpOptions.Host, smtpOptions.Port)
            {
                Credentials = new NetworkCredential(smtpOptions.UserName, smtpOptions.Password),
                EnableSsl = true
            }, smtpOptions.DefaultSender!, x.GetService<IWebHostEnvironment>()!));

            return services;
        }
    }
}
