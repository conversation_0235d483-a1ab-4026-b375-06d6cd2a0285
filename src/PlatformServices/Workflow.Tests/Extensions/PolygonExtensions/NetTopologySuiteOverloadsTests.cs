using FluentAssertions;
using NetTopologySuite.Geometries;
using TriangleNet.Geometry;
using Workflow.Extensions;
using Xunit;
using NtsGeometry = NetTopologySuite.Geometries.Geometry;
using NtsPolygon = NetTopologySuite.Geometries.Polygon;
using NtsCoordinate = NetTopologySuite.Geometries.Coordinate;

namespace Workflow.Tests.Extensions.PolygonExtensions;

[Trait("PolygonExtensions", "NetTopologySuiteOverloads")]
public class NetTopologySuiteOverloadsTests
{
    private readonly GeometryFactory _geometryFactory = new();

    [Fact(DisplayName = "AddExternal with NetTopologySuite Polygon should add external vertices")]
    public void AddExternal_WithNtsPolygon_ShouldAddExternalVertices()
    {
        // Arrange
        var triangleNetPolygon = new TriangleNet.Geometry.Polygon();
        var coordinates = new[]
        {
            new NtsCoordinate(0, 0),
            new NtsCoordinate(10, 0),
            new NtsCoordinate(10, 10),
            new NtsCoordinate(0, 10),
            new NtsCoordinate(0, 0)
        };
        var linearRing = _geometryFactory.CreateLinearRing(coordinates);
        var ntsPolygon = _geometryFactory.CreatePolygon(linearRing);

        // Act
        var result = triangleNetPolygon.AddExternal(ntsPolygon);

        // Assert
        result.Should().NotBeNull();
        result.Points.Should().HaveCountGreaterThan(0);
    }

    [Fact(DisplayName = "AddExternal with null geometry should throw ArgumentNullException")]
    public void AddExternal_WithNullGeometry_ShouldThrowArgumentNullException()
    {
        // Arrange
        var triangleNetPolygon = new TriangleNet.Geometry.Polygon();
        NtsGeometry nullGeometry = null;

        // Act & Assert
        var action = () => triangleNetPolygon.AddExternal(nullGeometry);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("externalGeometry");
    }

    [Fact(DisplayName = "AddMaterials with NetTopologySuite Polygon should add material vertices")]
    public void AddMaterials_WithNtsPolygon_ShouldAddMaterialVertices()
    {
        // Arrange
        var triangleNetPolygon = new TriangleNet.Geometry.Polygon();
        var coordinates = new[]
        {
            new NtsCoordinate(5, 5),
            new NtsCoordinate(15, 5),
            new NtsCoordinate(15, 15),
            new NtsCoordinate(5, 15),
            new NtsCoordinate(5, 5)
        };
        var linearRing = _geometryFactory.CreateLinearRing(coordinates);
        var ntsPolygon = _geometryFactory.CreatePolygon(linearRing);
        const int materialId = 1;

        // Act
        var result = triangleNetPolygon.AddMaterials(ntsPolygon, materialId);

        // Assert
        result.Should().NotBeNull();
        result.Points.Should().HaveCountGreaterThan(0);
    }

    [Fact(DisplayName = "AddMaterialsWithDeduplication with NetTopologySuite Polygon should add material vertices with deduplication")]
    public void AddMaterialsWithDeduplication_WithNtsPolygon_ShouldAddMaterialVerticesWithDeduplication()
    {
        // Arrange
        var triangleNetPolygon = new TriangleNet.Geometry.Polygon();
        var coordinates = new[]
        {
            new NtsCoordinate(2, 2),
            new NtsCoordinate(8, 2),
            new NtsCoordinate(8, 8),
            new NtsCoordinate(2, 8),
            new NtsCoordinate(2, 2)
        };
        var linearRing = _geometryFactory.CreateLinearRing(coordinates);
        var ntsPolygon = _geometryFactory.CreatePolygon(linearRing);
        const int materialId = 2;

        // Act
        var result = triangleNetPolygon.AddMaterialsWithDeduplication(ntsPolygon, materialId);

        // Assert
        result.Should().NotBeNull();
        result.Points.Should().HaveCountGreaterThan(0);
    }

    [Fact(DisplayName = "AddMaterials with multiple geometries should add all material vertices")]
    public void AddMaterials_WithMultipleGeometries_ShouldAddAllMaterialVertices()
    {
        // Arrange
        var triangleNetPolygon = new TriangleNet.Geometry.Polygon();
        
        var coordinates1 = new[]
        {
            new NtsCoordinate(0, 0),
            new NtsCoordinate(5, 0),
            new NtsCoordinate(5, 5),
            new NtsCoordinate(0, 5),
            new NtsCoordinate(0, 0)
        };
        var linearRing1 = _geometryFactory.CreateLinearRing(coordinates1);
        var polygon1 = _geometryFactory.CreatePolygon(linearRing1);

        var coordinates2 = new[]
        {
            new NtsCoordinate(10, 10),
            new NtsCoordinate(15, 10),
            new NtsCoordinate(15, 15),
            new NtsCoordinate(10, 15),
            new NtsCoordinate(10, 10)
        };
        var linearRing2 = _geometryFactory.CreateLinearRing(coordinates2);
        var polygon2 = _geometryFactory.CreatePolygon(linearRing2);

        var materialGeometries = new[]
        {
            (polygon1 as NtsGeometry, 1),
            (polygon2 as NtsGeometry, 2)
        };

        // Act
        var result = triangleNetPolygon.AddMaterials(materialGeometries);

        // Assert
        result.Should().NotBeNull();
        result.Points.Should().HaveCountGreaterThan(0);
    }

    [Fact(DisplayName = "AddMaterials with null geometries collection should throw ArgumentNullException")]
    public void AddMaterials_WithNullGeometriesCollection_ShouldThrowArgumentNullException()
    {
        // Arrange
        var triangleNetPolygon = new TriangleNet.Geometry.Polygon();
        IEnumerable<(NtsGeometry Geometry, int MaterialId)> nullCollection = null;

        // Act & Assert
        var action = () => triangleNetPolygon.AddMaterials(nullCollection);
        action.Should().Throw<ArgumentNullException>()
            .WithParameterName("materialGeometries");
    }

    [Fact(DisplayName = "AddExternal with LineString should extract coordinates correctly")]
    public void AddExternal_WithLineString_ShouldExtractCoordinatesCorrectly()
    {
        // Arrange
        var triangleNetPolygon = new TriangleNet.Geometry.Polygon();
        var coordinates = new[]
        {
            new NtsCoordinate(0, 0),
            new NtsCoordinate(10, 0),
            new NtsCoordinate(10, 10),
            new NtsCoordinate(0, 10)
        };
        var lineString = _geometryFactory.CreateLineString(coordinates);

        // Act
        var result = triangleNetPolygon.AddExternal(lineString);

        // Assert
        result.Should().NotBeNull();
        result.Points.Should().HaveCountGreaterThan(0);
    }

    [Fact(DisplayName = "AddExternal with Point should handle single coordinate")]
    public void AddExternal_WithPoint_ShouldHandleSingleCoordinate()
    {
        // Arrange
        var triangleNetPolygon = new TriangleNet.Geometry.Polygon();
        var point = _geometryFactory.CreatePoint(new NtsCoordinate(5, 5));

        // Act
        var result = triangleNetPolygon.AddExternal(point);

        // Assert
        result.Should().NotBeNull();
        result.Points.Should().HaveCount(1);
    }
}
